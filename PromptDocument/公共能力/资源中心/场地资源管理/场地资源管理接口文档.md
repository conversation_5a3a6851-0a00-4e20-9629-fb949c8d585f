# 资源中心 - 场地资源管理API接口文档

## 1. 接口概述

### 1.1 基础信息

- **基础路径**: `/publicbiz/site-management`
- **数据格式**: JSON
- **字符编码**: UTF-8
- **请求方式**: HTTP/HTTPS

### 1.2 统一响应格式

```json
{
  "code": 0,
  "message": "操作成功",
  "data": {},
  "timestamp": "2025-08-10T10:30:00Z"
}
```

### 1.3 状态码说明

- `0`: 操作成功
- `400`: 请求参数错误
- `401`: 未授权访问
- `403`: 权限不足
- `404`: 资源不存在
- `500`: 服务器内部错误

## 2. 场地管理接口

### 2.1 场地分页查询

**接口地址**: `GET /page`

**功能说明**: 分页查询场地列表，支持多条件筛选

**请求参数**:

| 参数名     | 类型    | 必填 | 说明                     |
| ---------- | ------- | ---- | ------------------------ |
| pageNum    | Integer | 是   | 页码，从1开始            |
| pageSize   | Integer | 是   | 每页大小，最大100        |
| campus     | String  | 否   | 校区筛选（支持校区编码） |
| campusName | String  | 否   | 校区名称筛选             |
| type       | String  | 否   | 场地类型筛选             |
| status     | String  | 否   | 状态筛选                 |
| capacity   | String  | 否   | 容量范围筛选             |
| keyword    | String  | 否   | 关键词搜索（场地名称）   |

**请求示例**:

```http
GET /publicbiz/site-management/page?pageNum=1&pageSize=10&campus=总部校区&campusName=总部校区&type=培训教室&status=可用&keyword=101
```

**响应字段**:

| 字段名              | 类型    | 说明         |
| ------------------- | ------- | ------------ |
| total               | Long    | 总记录数     |
| list                | Array   | 场地列表     |
| list[].id           | Long    | 场地ID       |
| list[].name         | String  | 场地名称     |
| list[].campus       | String  | 所属校区     |
| list[].campusName   | String  | 所属校区名称 |
| list[].type         | String  | 场地类型     |
| list[].location     | String  | 具体位置     |
| list[].seatTotal    | Integer | 总座位数     |
| list[].seatTypes    | Array   | 座位类型配置 |
| list[].seatDetail   | String  | 座位详情描述 |
| list[].equipment    | String  | 设备配置     |
| list[].status       | String  | 状态         |
| list[].description  | String  | 场地描述     |
| list[].manager      | String  | 负责人姓名   |
| list[].managerPhone | String  | 负责人电话   |
| list[].createTime   | String  | 创建时间     |
| list[].updateTime   | String  | 更新时间     |

**返回示例**:

```json
{
  "code": 0,
  "message": "查询成功",
  "data": {
    "total": 8,
    "list": [
      {
        "id": 1,
        "name": "总部A座101教室",
        "campus": "总部校区",
        "campusName": "总部校区",
        "type": "培训教室",
        "location": "A座1楼101室",
        "seatTotal": 50,
        "seatTypes": [
          {
            "name": "普通座",
            "count": 50,
            "remark": "标准课桌椅"
          }
        ],
        "seatDetail": "普通座:50座",
        "equipment": "投影仪、音响设备、空调、白板",
        "status": "可用",
        "description": "标准培训教室，适合各类技能培训",
        "manager": "张老师",
        "managerPhone": "13812345678",
        "createTime": "2025-01-10T09:00:00Z",
        "updateTime": "2025-01-10T09:00:00Z"
      }
    ]
  },
  "timestamp": "2025-08-10T10:30:00Z"
}
```

### 2.2 场地列表查询

**接口地址**: `GET /list`

**功能说明**: 查询所有场地列表（不分页），用于下拉选择等场景

**请求参数**:

| 参数名     | 类型   | 必填 | 说明                     |
| ---------- | ------ | ---- | ------------------------ |
| campus     | String | 否   | 校区筛选（支持校区编码） |
| campusName | String | 否   | 校区名称筛选             |
| type       | String | 否   | 场地类型筛选             |
| status     | String | 否   | 状态筛选                 |
| name       | String | 否   | 场地名称筛选             |

**请求示例**:

```http
GET /publicbiz/site-management/list?status=可用&campusName=总部校区&name=教室
```

**响应字段**:

| 字段名            | 类型    | 说明         |
| ----------------- | ------- | ------------ |
| data              | Array   | 场地列表     |
| data[].id         | Long    | 场地ID       |
| data[].name       | String  | 场地名称     |
| data[].campus     | String  | 所属校区     |
| data[].campusName | String  | 所属校区名称 |
| data[].type       | String  | 场地类型     |
| data[].seatTotal  | Integer | 总座位数     |
| data[].status     | String  | 状态         |

**返回示例**:

```json
{
  "code": 0,
  "message": "查询成功",
  "data": [
    {
      "id": 1,
      "name": "总部A座101教室",
      "campus": "总部校区",
      "campusName": "总部校区",
      "type": "培训教室",
      "seatTotal": 50,
      "status": "可用"
    }
  ],
  "timestamp": "2025-08-10T10:30:00Z"
}
```

### 2.3 新增场地

**接口地址**: `POST /create`

**功能说明**: 新增场地信息

**请求参数**:

| 参数名             | 类型    | 必填 | 说明                     |
| ------------------ | ------- | ---- | ------------------------ |
| name               | String  | 是   | 场地名称，最大30字符     |
| campus             | String  | 是   | 所属校区                 |
| campusName         | String  | 是   | 所属校区名称，最大50字符 |
| type               | String  | 是   | 场地类型                 |
| location           | String  | 是   | 具体位置，最大30字符     |
| seatTypes          | Array   | 是   | 座位类型配置             |
| seatTypes[].name   | String  | 是   | 座位类型名称             |
| seatTypes[].count  | Integer | 是   | 座位数量                 |
| seatTypes[].remark | String  | 否   | 备注                     |
| equipment          | String  | 否   | 设备配置，最大200字符    |
| status             | String  | 是   | 状态                     |
| description        | String  | 否   | 场地描述，最大200字符    |
| manager            | String  | 是   | 负责人姓名，最大10字符   |
| managerPhone       | String  | 是   | 负责人电话，最大20字符   |

**请求示例**:

```json
POST /publicbiz/site-management/create
Content-Type: application/json

{
  "name": "总部A座101教室",
  "campus": "总部校区",
  "campusName": "总部校区",
  "type": "培训教室",
  "location": "A座1楼101室",
  "seatTypes": [
    {
      "name": "普通座",
      "count": 50,
      "remark": "标准课桌椅"
    }
  ],
  "equipment": "投影仪、音响设备、空调、白板",
  "status": "可用",
  "description": "标准培训教室，适合各类技能培训",
  "manager": "张老师",
  "managerPhone": "13812345678"
}
```

**响应字段**:

| 字段名 | 类型 | 说明         |
| ------ | ---- | ------------ |
| data   | Long | 新增场地的ID |

**返回示例**:

```json
{
  "code": 0,
  "message": "新增成功",
  "data": 1,
  "timestamp": "2025-08-10T10:30:00Z"
}
```

### 2.4 编辑场地

**接口地址**: `PUT /update`

**功能说明**: 编辑场地信息

**请求参数**:

| 参数名             | 类型    | 必填 | 说明                     |
| ------------------ | ------- | ---- | ------------------------ |
| id                 | Long    | 是   | 场地ID                   |
| name               | String  | 是   | 场地名称，最大30字符     |
| campus             | String  | 是   | 所属校区                 |
| campusName         | String  | 是   | 所属校区名称，最大50字符 |
| type               | String  | 是   | 场地类型                 |
| location           | String  | 是   | 具体位置，最大30字符     |
| seatTypes          | Array   | 是   | 座位类型配置             |
| seatTypes[].name   | String  | 是   | 座位类型名称             |
| seatTypes[].count  | Integer | 是   | 座位数量                 |
| seatTypes[].remark | String  | 否   | 备注                     |
| equipment          | String  | 否   | 设备配置，最大200字符    |
| status             | String  | 是   | 状态                     |
| description        | String  | 否   | 场地描述，最大200字符    |
| manager            | String  | 是   | 负责人姓名，最大10字符   |
| managerPhone       | String  | 是   | 负责人电话，最大20字符   |

**请求示例**:

```json
PUT /publicbiz/site-management/update
Content-Type: application/json

{
  "id": 1,
  "name": "总部A座101教室",
  "campus": "总部校区",
  "campusName": "总部校区",
  "type": "培训教室",
  "location": "A座1楼101室",
  "seatTypes": [
    {
      "name": "普通座",
      "count": 60,
      "remark": "升级后的课桌椅"
    }
  ],
  "equipment": "投影仪、音响设备、空调、白板、电子屏",
  "status": "可用",
  "description": "标准培训教室，适合各类技能培训",
  "manager": "张老师",
  "managerPhone": "13812345678"
}
```

**返回示例**:

```json
{
  "code": 0,
  "message": "更新成功",
  "data": true,
  "timestamp": "2025-08-10T10:30:00Z"
}
```

### 2.5 删除场地

**接口地址**: `DELETE /delete/{id}`

**功能说明**: 删除场地信息（软删除）

**请求参数**:

| 参数名 | 类型 | 必填 | 说明               |
| ------ | ---- | ---- | ------------------ |
| id     | Long | 是   | 场地ID（路径参数） |

**请求示例**:

```http
DELETE /publicbiz/site-management/delete/1
```

**返回示例**:

```json
{
  "code": 0,
  "message": "删除成功",
  "data": true,
  "timestamp": "2025-08-10T10:30:00Z"
}
```

### 2.6 场地详情查询

**接口地址**: `GET /detail/{id}`

**功能说明**: 根据ID查询场地详细信息

**请求参数**:

| 参数名 | 类型 | 必填 | 说明               |
| ------ | ---- | ---- | ------------------ |
| id     | Long | 是   | 场地ID（路径参数） |

**请求示例**:

```http
GET /publicbiz/site-management/detail/1
```

**响应字段**:

| 字段名            | 类型    | 说明         |
| ----------------- | ------- | ------------ |
| data              | Object  | 场地详情     |
| data.id           | Long    | 场地ID       |
| data.name         | String  | 场地名称     |
| data.campus       | String  | 所属校区     |
| data.campusName   | String  | 所属校区名称 |
| data.type         | String  | 场地类型     |
| data.location     | String  | 具体位置     |
| data.seatTotal    | Integer | 总座位数     |
| data.seatTypes    | Array   | 座位类型配置 |
| data.seatDetail   | String  | 座位详情描述 |
| data.equipment    | String  | 设备配置     |
| data.status       | String  | 状态         |
| data.description  | String  | 场地描述     |
| data.manager      | String  | 负责人姓名   |
| data.managerPhone | String  | 负责人电话   |
| data.createTime   | String  | 创建时间     |
| data.updateTime   | String  | 更新时间     |

**返回示例**:

```json
{
  "code": 0,
  "message": "查询成功",
  "data": {
    "id": 1,
    "name": "总部A座101教室",
    "campus": "总部校区",
    "campusName": "总部校区",
    "type": "培训教室",
    "location": "A座1楼101室",
    "seatTotal": 50,
    "seatTypes": [
      {
        "name": "普通座",
        "count": 50,
        "remark": "标准课桌椅"
      }
    ],
    "seatDetail": "普通座:50座",
    "equipment": "投影仪、音响设备、空调、白板",
    "status": "可用",
    "description": "标准培训教室，适合各类技能培训",
    "manager": "张老师",
    "managerPhone": "13812345678",
    "createTime": "2025-01-10T09:00:00Z",
    "updateTime": "2025-01-10T09:00:00Z"
  },
  "timestamp": "2025-08-10T10:30:00Z"
}
```

### 2.7 场地统计报表

**接口地址**: `GET /statistics`

**功能说明**: 获取场地统计数据，包括总数、可用、已预约、维护中等

**请求参数**:

| 参数名     | 类型   | 必填 | 说明                     |
| ---------- | ------ | ---- | ------------------------ |
| campus     | String | 否   | 校区筛选（支持校区编码） |
| campusName | String | 否   | 校区名称筛选             |

**请求示例**:

```http
GET /publicbiz/site-management/statistics?campus=总部校区&campusName=总部校区
```

**响应字段**:

| 字段名                | 类型    | 说明         |
| --------------------- | ------- | ------------ |
| data                  | Object  | 统计数据     |
| data.totalCount       | Integer | 场地总数     |
| data.availableCount   | Integer | 可用场地数   |
| data.reservedCount    | Integer | 已预约场地数 |
| data.maintenanceCount | Integer | 维护中场地数 |
| data.disabledCount    | Integer | 停用场地数   |

**返回示例**:

```json
{
  "code": 0,
  "message": "查询成功",
  "data": {
    "totalCount": 8,
    "availableCount": 5,
    "reservedCount": 1,
    "maintenanceCount": 1,
    "disabledCount": 1
  },
  "timestamp": "2025-08-10T10:30:00Z"
}
```

## 3. 场地预约接口

### 3.1 新增预约

**接口地址**: `POST /appointment/create`

**功能说明**: 新增场地预约

**请求参数**:

| 参数名       | 类型    | 必填 | 说明                       |
| ------------ | ------- | ---- | -------------------------- |
| siteId       | Long    | 是   | 场地ID                     |
| activityName | String  | 是   | 活动名称，最大30字符       |
| activityType | String  | 是   | 活动类型                   |
| startDate    | String  | 是   | 开始日期，格式：YYYY-MM-DD |
| endDate      | String  | 是   | 结束日期，格式：YYYY-MM-DD |
| startTime    | String  | 是   | 开始时间，格式：HH:mm      |
| endTime      | String  | 是   | 结束时间，格式：HH:mm      |
| peopleCount  | Integer | 是   | 预计人数                   |
| contactName  | String  | 是   | 联系人姓名，最大10字符     |
| contactPhone | String  | 是   | 联系电话，最大20字符       |
| status       | String  | 否   | 预约状态，默认"已确认"     |
| remark       | String  | 否   | 备注信息，最大200字符      |

**请求示例**:

```json
POST /publicbiz/site-management/appointment/create
Content-Type: application/json

{
  "siteId": 1,
  "activityName": "家政服务员技能培训",
  "activityType": "培训",
  "startDate": "2025-08-15",
  "endDate": "2025-08-15",
  "startTime": "09:00",
  "endTime": "17:00",
  "peopleCount": 35,
  "contactName": "张老师",
  "contactPhone": "13812345678",
  "status": "已确认",
  "remark": "需要投影设备和音响"
}
```

**响应字段**:

| 字段名 | 类型 | 说明         |
| ------ | ---- | ------------ |
| data   | Long | 新增预约的ID |

**返回示例**:

```json
{
  "code": 0,
  "message": "预约成功",
  "data": 1,
  "timestamp": "2025-08-10T10:30:00Z"
}
```

### 3.2 预约分页查询

**接口地址**: `GET /appointment/page`

**功能说明**: 分页查询预约列表，支持多条件筛选

**请求参数**:

| 参数名       | 类型    | 必填 | 说明                           |
| ------------ | ------- | ---- | ------------------------------ |
| pageNum      | Integer | 是   | 页码，从1开始                  |
| pageSize     | Integer | 是   | 每页大小，最大100              |
| siteId       | Long    | 否   | 场地ID筛选                     |
| activityType | String  | 否   | 活动类型筛选                   |
| status       | String  | 否   | 预约状态筛选                   |
| startDate    | String  | 否   | 开始日期筛选，格式：YYYY-MM-DD |
| endDate      | String  | 否   | 结束日期筛选，格式：YYYY-MM-DD |
| contactName  | String  | 否   | 联系人姓名筛选                 |

**请求示例**:

```http
GET /publicbiz/site-management/appointment/page?pageNum=1&pageSize=10&siteId=1&activityType=培训&status=已确认
```

**响应字段**:

| 字段名                | 类型    | 说明             |
| --------------------- | ------- | ---------------- |
| total                 | Long    | 总记录数         |
| list                  | Array   | 预约列表         |
| list[].id             | Long    | 预约ID           |
| list[].siteId         | Long    | 场地ID           |
| list[].siteName       | String  | 场地名称         |
| list[].siteCampusName | String  | 场地所属校区名称 |
| list[].activityName   | String  | 活动名称         |
| list[].activityType   | String  | 活动类型         |
| list[].startDate      | String  | 开始日期         |
| list[].endDate        | String  | 结束日期         |
| list[].startTime      | String  | 开始时间         |
| list[].endTime        | String  | 结束时间         |
| list[].timeRange      | String  | 时间段描述       |
| list[].peopleCount    | Integer | 预计人数         |
| list[].contactName    | String  | 联系人姓名       |
| list[].contactPhone   | String  | 联系电话         |
| list[].status         | String  | 预约状态         |
| list[].remark         | String  | 备注信息         |
| list[].createTime     | String  | 创建时间         |

**返回示例**:

```json
{
  "code": 0,
  "message": "查询成功",
  "data": {
    "total": 5,
    "list": [
      {
        "id": 1,
        "siteId": 1,
        "siteName": "总部A座101教室",
        "siteCampusName": "总部校区",
        "activityName": "家政服务员技能培训",
        "activityType": "培训",
        "startDate": "2025-08-15",
        "endDate": "2025-08-15",
        "startTime": "09:00",
        "endTime": "17:00",
        "timeRange": "09:00-17:00",
        "peopleCount": 35,
        "contactName": "张老师",
        "contactPhone": "13812345678",
        "status": "已确认",
        "remark": "需要投影设备和音响",
        "createTime": "2025-08-10T09:00:00Z"
      }
    ]
  },
  "timestamp": "2025-08-10T10:30:00Z"
}
```

### 3.3 预约详情查询

**接口地址**: `GET /appointment/detail/{id}`

**功能说明**: 根据ID查询预约详细信息

**请求参数**:

| 参数名 | 类型 | 必填 | 说明               |
| ------ | ---- | ---- | ------------------ |
| id     | Long | 是   | 预约ID（路径参数） |

**请求示例**:

```http
GET /publicbiz/site-management/appointment/detail/1
```

**响应字段**:

| 字段名              | 类型    | 说明             |
| ------------------- | ------- | ---------------- |
| data                | Object  | 预约详情         |
| data.id             | Long    | 预约ID           |
| data.siteId         | Long    | 场地ID           |
| data.siteName       | String  | 场地名称         |
| data.siteCampusName | String  | 场地所属校区名称 |
| data.activityName   | String  | 活动名称         |
| data.activityType   | String  | 活动类型         |
| data.startDate      | String  | 开始日期         |
| data.endDate        | String  | 结束日期         |
| data.startTime      | String  | 开始时间         |
| data.endTime        | String  | 结束时间         |
| data.timeRange      | String  | 时间段描述       |
| data.peopleCount    | Integer | 预计人数         |
| data.contactName    | String  | 联系人姓名       |
| data.contactPhone   | String  | 联系电话         |
| data.status         | String  | 预约状态         |
| data.remark         | String  | 备注信息         |
| data.createTime     | String  | 创建时间         |
| data.updateTime     | String  | 更新时间         |

**返回示例**:

```json
{
  "code": 0,
  "message": "查询成功",
  "data": {
    "id": 1,
    "siteId": 1,
    "siteName": "总部A座101教室",
    "siteCampusName": "总部校区",
    "activityName": "家政服务员技能培训",
    "activityType": "培训",
    "startDate": "2025-08-15",
    "endDate": "2025-08-15",
    "startTime": "09:00",
    "endTime": "17:00",
    "timeRange": "09:00-17:00",
    "peopleCount": 35,
    "contactName": "张老师",
    "contactPhone": "13812345678",
    "status": "已确认",
    "remark": "需要投影设备和音响",
    "createTime": "2025-08-10T09:00:00Z",
    "updateTime": "2025-08-10T09:00:00Z"
  },
  "timestamp": "2025-08-10T10:30:00Z"
}
```

### 3.4 取消预约

**接口地址**: `PUT /appointment/cancel/{id}`

**功能说明**: 取消预约（更新状态为已取消）

**请求参数**:

| 参数名 | 类型 | 必填 | 说明               |
| ------ | ---- | ---- | ------------------ |
| id     | Long | 是   | 预约ID（路径参数） |

**请求示例**:

```http
PUT /publicbiz/site-management/appointment/cancel/1
```

**返回示例**:

```json
{
  "code": 0,
  "message": "取消成功",
  "data": true,
  "timestamp": "2025-08-10T10:30:00Z"
}
```

### 3.5 根据场地和日期查询预约

**接口地址**: `GET /appointment/listBySiteAndDate`

**功能说明**: 根据场地ID和日期（支持日期区间）查询预约列表，用于排期看板

**请求参数**:

| 参数名    | 类型   | 必填 | 说明                           |
| --------- | ------ | ---- | ------------------------------ |
| siteId    | Long   | 是   | 场地ID                         |
| date      | String | 否   | 单个日期查询，格式：YYYY-MM-DD |
| startDate | String | 否   | 开始日期，格式：YYYY-MM-DD     |
| endDate   | String | 否   | 结束日期，格式：YYYY-MM-DD     |

**参数使用说明**:

- 可以使用 `date` 参数查询单个日期的预约
- 可以使用 `startDate` 和 `endDate` 参数查询日期区间的预约
- `date` 参数与 `startDate`/`endDate` 参数不能同时使用

**请求示例**:

单日期查询示例：

```http
GET /publicbiz/site-management/appointment/listBySiteAndDate?siteId=1&date=2025-08-15
```

日期区间查询示例：

```http
GET /publicbiz/site-management/appointment/listBySiteAndDate?siteId=1&startDate=2025-08-15&endDate=2025-08-20
```

**响应字段**:

| 字段名              | 类型    | 说明       |
| ------------------- | ------- | ---------- |
| data                | Array   | 预约列表   |
| data[].id           | Long    | 预约ID     |
| data[].activityName | String  | 活动名称   |
| data[].activityType | String  | 活动类型   |
| data[].timeRange    | String  | 时间段     |
| data[].peopleCount  | Integer | 预计人数   |
| data[].contactName  | String  | 联系人姓名 |
| data[].contactPhone | String  | 联系电话   |
| data[].status       | String  | 预约状态   |
| data[].remark       | String  | 备注信息   |

**返回示例**:

```json
{
  "code": 0,
  "message": "查询成功",
  "data": [
    {
      "id": 1,
      "activityName": "家政服务员技能培训",
      "activityType": "培训",
      "timeRange": "09:00-17:00",
      "peopleCount": 35,
      "contactName": "张老师",
      "contactPhone": "13812345678",
      "status": "已确认",
      "remark": "需要投影设备和音响"
    },
    {
      "id": 2,
      "activityName": "月嫂培训考试",
      "activityType": "考试",
      "timeRange": "14:00-16:00",
      "peopleCount": 20,
      "contactName": "李老师",
      "contactPhone": "13987654321",
      "status": "已确认",
      "remark": "考试专用，需要安静环境"
    }
  ],
  "timestamp": "2025-08-10T10:30:00Z"
}
```

### 3.6 预约列表查询

**接口地址**: `GET /appointment/list`

**功能说明**: 查询所有预约列表（不分页），用于下拉选择等场景

**请求参数**:

| 参数名       | 类型   | 必填 | 说明                           |
| ------------ | ------ | ---- | ------------------------------ |
| siteId       | Long   | 否   | 场地ID筛选                     |
| activityType | String | 否   | 活动类型筛选                   |
| status       | String | 否   | 预约状态筛选                   |
| startDate    | String | 否   | 开始日期筛选，格式：YYYY-MM-DD |
| endDate      | String | 否   | 结束日期筛选，格式：YYYY-MM-DD |
| contactName  | String | 否   | 联系人姓名筛选                 |

**请求示例**:

```http
GET /publicbiz/site-management/appointment/list?siteId=1&activityType=培训&status=已确认
```

**响应字段**:

| 字段名                | 类型    | 说明             |
| --------------------- | ------- | ---------------- |
| data                  | Array   | 预约列表         |
| data[].id             | Long    | 预约ID           |
| data[].siteId         | Long    | 场地ID           |
| data[].siteName       | String  | 场地名称         |
| data[].siteCampusName | String  | 场地所属校区名称 |
| data[].activityName   | String  | 活动名称         |
| data[].activityType   | String  | 活动类型         |
| data[].startDate      | String  | 开始日期         |
| data[].endDate        | String  | 结束日期         |
| data[].startTime      | String  | 开始时间         |
| data[].endTime        | String  | 结束时间         |
| data[].timeRange      | String  | 时间段描述       |
| data[].peopleCount    | Integer | 预计人数         |
| data[].contactName    | String  | 联系人姓名       |
| data[].contactPhone   | String  | 联系电话         |
| data[].status         | String  | 预约状态         |
| data[].remark         | String  | 备注信息         |
| data[].createTime     | String  | 创建时间         |

**返回示例**:

```json
{
  "code": 0,
  "message": "查询成功",
  "data": [
    {
      "id": 1,
      "siteId": 1,
      "siteName": "总部A座101教室",
      "siteCampusName": "总部校区",
      "activityName": "家政服务员技能培训",
      "activityType": "培训",
      "startDate": "2025-08-15",
      "endDate": "2025-08-15",
      "startTime": "09:00",
      "endTime": "17:00",
      "timeRange": "09:00-17:00",
      "peopleCount": 35,
      "contactName": "张老师",
      "contactPhone": "13812345678",
      "status": "已确认",
      "remark": "需要投影设备和音响",
      "createTime": "2025-08-10T09:00:00Z"
    },
    {
      "id": 2,
      "siteId": 2,
      "siteName": "总部B座201会议室",
      "siteCampusName": "总部校区",
      "activityName": "月嫂培训考试",
      "activityType": "考试",
      "startDate": "2025-08-16",
      "endDate": "2025-08-16",
      "startTime": "14:00",
      "endTime": "16:00",
      "timeRange": "14:00-16:00",
      "peopleCount": 20,
      "contactName": "李老师",
      "contactPhone": "13987654321",
      "status": "已确认",
      "remark": "考试专用，需要安静环境",
      "createTime": "2025-08-10T10:00:00Z"
    }
  ],
  "timestamp": "2025-08-10T10:30:00Z"
}
```

## 4. 数据字典

### 4.1 场地类型 (type)

| 值       | 说明             |
| -------- | ---------------- |
| 培训教室 | 用于各类技能培训 |
| 多功能厅 | 大型活动、讲座等 |
| 会议室   | 会议、研讨等     |
| 实训室   | 实操训练         |
| 考试场地 | 考试专用         |
| 研讨室   | 小型研讨、交流   |

### 4.2 场地状态 (status)

| 值     | 说明               |
| ------ | ------------------ |
| 可用   | 正常可预约使用     |
| 已预约 | 已被预约占用       |
| 维护中 | 设备维护，暂不可用 |
| 停用   | 停止使用           |

### 4.3 活动类型 (activityType)

| 值   | 说明               |
| ---- | ------------------ |
| 培训 | 技能培训、教学活动 |
| 考试 | 各类考试、测评     |
| 会议 | 会议、座谈         |
| 讲座 | 讲座、宣讲         |
| 其他 | 其他类型活动       |

### 4.4 预约状态 (appointmentStatus)

| 值     | 说明                   |
| ------ | ---------------------- |
| 已确认 | 预约已确认，可正常使用 |
| 待确认 | 预约待管理员确认       |
| 已取消 | 预约已取消             |

## 5. 错误码说明

### 5.1 通用错误码

| 错误码 | 说明           | 解决方案                 |
| ------ | -------------- | ------------------------ |
| 400    | 请求参数错误   | 检查请求参数格式和必填项 |
| 401    | 未授权访问     | 检查登录状态和token      |
| 403    | 权限不足       | 联系管理员分配相应权限   |
| 404    | 资源不存在     | 检查资源ID是否正确       |
| 500    | 服务器内部错误 | 联系技术支持             |

### 5.2 业务错误码

| 错误码 | 说明               | 解决方案                             |
| ------ | ------------------ | ------------------------------------ |
| 10001  | 场地名称已存在     | 使用不同的场地名称                   |
| 10002  | 场地不存在         | 检查场地ID是否正确                   |
| 10003  | 场地状态不允许预约 | 选择状态为"可用"的场地               |
| 10004  | 时间段冲突         | 选择其他时间段或场地                 |
| 10005  | 预约不存在         | 检查预约ID是否正确                   |
| 10006  | 预约状态不允许取消 | 只能取消"已确认"或"待确认"状态的预约 |
| 10007  | 座位数量不足       | 选择容量更大的场地或减少人数         |

## 6. 接口调用示例

### 6.1 完整的场地管理流程

```javascript
// 1. 查询场地统计
const stats = await fetch('/publicbiz/site-management/statistics')

// 2. 分页查询场地列表
const sites = await fetch('/publicbiz/site-management/page?pageNum=1&pageSize=10')

// 3. 新增场地
const newSite = await fetch('/publicbiz/site-management/create', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    name: '新教室',
    campus: '总部校区',
    campusName: '总部校区',
    type: '培训教室',
    location: 'A座2楼201室',
    seatTypes: [{ name: '普通座', count: 40, remark: '' }],
    equipment: '投影仪、音响',
    status: '可用',
    manager: '张老师',
    managerPhone: '13812345678'
  })
})

// 4. 预约场地
const appointment = await fetch('/publicbiz/site-management/appointment/create', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    siteId: 1,
    activityName: '技能培训',
    activityType: '培训',
    startDate: '2025-08-20',
    endDate: '2025-08-20',
    startTime: '09:00',
    endTime: '17:00',
    peopleCount: 30,
    contactName: '李老师',
    contactPhone: '13987654321'
  })
})
```

## 7. 注意事项

1. **时间格式**: 所有日期使用 `YYYY-MM-DD` 格式，时间使用 `HH:mm` 格式
2. **字符编码**: 请求和响应均使用 UTF-8 编码
3. **分页参数**: pageNum 从 1 开始，pageSize 最大值为 100
4. **座位配置**: seatTypes 为 JSON 数组，支持多种座位类型
5. **状态管理**: 场地状态会影响预约功能，只有"可用"状态的场地才能被预约
6. **时间冲突**: 系统会自动检查预约时间冲突，同一场地同一时间段只能有一个预约
7. **软删除**: 删除操作为软删除，数据不会物理删除
8. **权限控制**: 部分接口需要相应的权限才能访问
9. **校区字段**: `campus` 为校区编码，`campusName` 为校区名称，两者可同时使用进行筛选
10. **字段命名**: 所有字段采用驼峰命名规范，与前端保持一致

---

**文档版本**: v1.1 **最后更新**: 2025-08-10 **维护人员**: 开发团队 **更新内容**: 新增 campusName 字段支持
